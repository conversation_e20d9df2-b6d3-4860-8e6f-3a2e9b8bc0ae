# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_PATH=./database/migration.db

# Source API Configuration (e.g., Freshdesk source)
SOURCE_API_URL=https://pandohelp.freshdesk.com/api/v2
SOURCE_API_KEY=********************
SOURCE_API_TYPE=freshdesk

# Target API Configuration (e.g., Freshdesk target)
TARGET_API_URL=https://kambaa1726.freshdesk.com/api/v2
TARGET_API_KEY=********************
TARGET_API_TYPE=freshdesk

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_DELAY_MS=1000

# Migration Configuration
BATCH_SIZE=50
MAX_RETRIES=3
RETRY_DELAY_MS=5000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/migration.log

# Security
JWT_SECRET=your_jwt_secret_here
BCRYPT_ROUNDS=10

# WebSocket Configuration
SOCKET_CORS_ORIGIN=http://localhost:3000
